from tqdm import tqdm  # For progress tracking
import os
import re
import traceback
import time
import random
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
import requests
import threading
import itertools
from pathlib import Path
import requests
import argparse
import yaml
import hashlib
import prompt_db
from utils import (
    read_jsonl,
    write_jsonl,
    add_jsonl
)

# from code_eval_X1.models.x1_test import get_eb4t04

API_URL_LIST = []

config = None
with open('config.yaml') as f:
    config = yaml.safe_load(f)

MODEL_NAME = config["server"]["model_name"]

class ActiveRequestsLoadBalancer:
    """基于活跃请求数的负载均衡器"""
    
    def __init__(self, api_endpoints):
        """
            初始化API调用器。
        
        Args:
            api_endpoints (List[str]): API端点列表, 将被并发调用。
        
        Returns:
            None
        
        Raises:
            None
        """
        
        assert len(api_endpoints) > 0
        self.api_endpoints = api_endpoints
        self.active_requests = {api: 0 for api in api_endpoints}  # 每个API的活跃请求数
        self.lock = threading.Lock()  # 线程安全锁
    
    def select_api(self):
        """选择当前活跃请求数最少的API"""
        with self.lock:
            # 找到活跃请求数最少的API
            min_requests = min(self.active_requests.values())
            candidates = [api for api, count in self.active_requests.items() if count == min_requests]
            
            # 如果有多个候选，随机选择一个
            if len(candidates) > 1:
                api = random.choice(candidates)
            else:
                api = candidates[0]
            
            # 增加该API的活跃请求数
            self.active_requests[api] += 1
            # logger.debug(f"选择API: {api}, 活跃请求: {self.active_requests[api]}")
            return api
    
    def request_complete(self, api: str):
        """标记API请求完成, 减少活跃请求数"""
        with self.lock:
            self.active_requests[api] -= 1
            if self.active_requests[api] < 0:
                self.active_requests[api] = 0
    
    def get_status(self):
        """获取当前负载状态"""
        with self.lock:
            return self.active_requests.copy()


load_balancer = None

def check_configs():
    """ 
    check configs
    """
    print("Nothing for config checking right now...")
    print("Config file check successful!")


def check_link(link, headers, payload):
    """
    check_link
    """
    try:
        response = requests.post(link, headers=headers, json=payload)
        if response.status_code == 200:
            print(f"Valid link: {link}")
            return link
        else:
            print(f"Invalid link (HTTP {response.status_code}): {link}")
    except requests.exceptions.RequestException as e:
        print(f"Error checking {link}: {str(e)}")
        
    return None

    
def filter_valid_links(links):
    """
    Check a list of links and return only those that respond successfully to an API request.
    
    Args:
        links: List of URLs to check
        
    Returns:
        List of valid URLs that responded successfully
    """
    random.shuffle(links)
    valid_links = []
    
    headers = {
        "Content-Type": "application/json"
    }
    
    model_name = config["server"]["model_name"]
    model_type = config["server"]["model_type"]
    
    payload = None

    if model_type == "qwen3":
        payload = {
            "model": model_name,
            "messages": [
                {"role": "user", "content": " how many letter r are in strawberry?"}
            ],
            "temperature": 0.7,
            "top_p": 0.8,
            "top_k": 20,
            "max_tokens": 32,
            "presence_penalty": 1.5,
            "chat_template_kwargs": {"enable_thinking": False}
        }
                    
    elif model_type == "gptoss":
        payload = {
            "input": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "how many letter r are in strawberry?"}
            ],
            "max_output_tokens": 32,
            "temperature": 1.0,
            "top_p": 1.0,
            "reasoning": {"effort": "high"}
        }
    else:
        return None

    # use threadpool to enhance the speed
    with ThreadPoolExecutor(max_workers=200) as executor:
        future_to_link = {executor.submit(check_link, link, headers, payload): link for link in links}
        
        for future in as_completed(future_to_link):
            result = future.result()
            if result != None:
                valid_links.append(result)

    return valid_links


def send_requests(prompt, url, quick_think=True, model_type="qwen3"):
    """
    send_requests
    """

    if model_type == "qwen3":
        headers = {
            "Content-Type": "application/json"
        }
            
        data = {
            "model": MODEL_NAME,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7, # 官方推荐非thinking 0.7
            "top_p": 0.8,
            "top_k": 20,
            "max_tokens": 16000,
            "presence_penalty": 1.5,
            "chat_template_kwargs": {"enable_thinking": not quick_think}
        }

        response = requests.post(url, headers=headers, data=json.dumps(data))

        if response.status_code == 200:
            response = response.json()
            response = response["choices"][0]["message"]["content"]
            # print(f"{response}")
            return response
        else:
            print(response.json())
            # print(prompt)
            response.raise_for_status()  # Raise an error if the request failed

    elif model_type == "gptoss":
        reasoning_effort="high"
        if quick_think:
            reasoning_effort="low"

        headers = {
            "Content-Type": "application/json"
        }
        data = {
            "input": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            "max_output_tokens": 12000,
            "temperature": 1.0,
            "top_p": 1.0,
            "reasoning": {"effort": "high"}
        }

        response = requests.post(url, headers=headers, data=json.dumps(data)) 

        if response.status_code == 200:
            response = response.json()
            # print(response)
            gptoss_thinking = response["output"][0]["content"][0]["text"]
            gptoss_final_answer = response["output"][1]["content"][0]["text"]
            return {
                "thinking" : gptoss_thinking,
                "final_answer" : gptoss_final_answer
            }
        else:
            response.raise_for_status()  # Raise an error if the request failed
            return None

    else:
        return None


def hash_text(text, algorithm='sha256'):
    # Create a new hash object using the specified algorithm
    hash_obj = hashlib.new(algorithm)
    
    # Update the hash object with the bytes of the text
    hash_obj.update(text.encode('utf-8'))
    
    # Return the hexadecimal digest of the hash
    return hash_obj.hexdigest()


def send_requests_general(prompt, url, temperature=0.7, top_p=0.8, quick_think=True):
    """
    send_requests_general
    """
    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "model": MODEL_NAME,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7, # 官方推荐非thinking 0.7
        "top_p": 0.8,
        "max_tokens": 8192,
        "presence_penalty": 1.5,
        "chat_template_kwargs": {"enable_thinking": not quick_think}
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    if response.status_code == 200:
        response = response.json()
        response = response["choices"][0]["message"]["content"]
        # print(f"{response}")
        return response
    else:
        print(response.json())
        # print(prompt)
        response.raise_for_status()  # Raise an error if the request failed


def process_api_urls():
    """
    process_api_urls
    """
    api_url_list = []
    use_exact_ips = config['server']['use_exact_api_links']
    use_local_machine = config['server']['use_local_machine']
    model_type = config['server']['model_type']

    if use_exact_ips:
        api_url_list = config['server']['exact_api_links'].split(",")
    else:
        ips = []
        if not use_local_machine:
            ips = config['server']['ips'].split("|")
        else:
            try:
                with open('local_ip.txt', 'r') as f:
                    ip = f.read().strip()
                    ips = [ip]
                print("Using local IP directly, instead of predefined ips.")
                print(f"IP address: {ip}")
                print()
            except Exception as e:
                print(f"Error: {e}")
                exit(1)

        base_port = config['server']['base_port']
        max_port = config['server']['max_port']

        for ip in ips:
            for port in range(base_port, max_port + 1):
                customized_link = None
                if model_type == "qwen3":
                    customized_link = f"http://{ip}:{port}/v1/chat/completions"
                elif  model_type == "gptoss":
                    customized_link = f"http://{ip}:{port}/v1/responses"
                else:
                    raise ValueError("Invalid model type when doing prechecking")
                api_url_list.append(customized_link)
                   
    api_url_list = filter_valid_links(api_url_list)  
    global API_URL_LIST
    API_URL_LIST = api_url_list

    if len(API_URL_LIST) == 0:
        print("No valid api urls")
        print("Exiting the program...")
        exit(1)

    print(f"Num of valid api urls: {len(API_URL_LIST)}")
    print(f"Valid API urls {API_URL_LIST}")


def register_workload_balancer():
    """
    """
    global load_balancer
    print("Preparing to register workload balancer...")
    load_balancer = ActiveRequestsLoadBalancer(API_URL_LIST)
    print("Successfully registered workload balancer!")


def get_args():
    """
    get_args
    """
    parser = argparse.ArgumentParser(description='main')
    parser.add_argument('--input_file', type=str, default=None)
    parser.add_argument('--output_file', type=str, default=None)
    
    return parser.parse_args()


def extract_box(result):
    """
    Extracts the content within \box{} or \boxed{} from a given string.

    :param text: The input string containing \box{} or \boxed{}.
    :return: A list of extracted content.
    """
    # Use a regular expression to find all occurrences of \box{} or \boxed{}
    pattern = r'\\box(?:ed)?\{((?:[^\{\}]|\{(?:[^\{\}]|\{[^\{\}]*\})*\})*)\}'
    matches = re.findall(pattern, result)
    
    if len(matches) >= 1:
        return matches[-1]
    else:
        return ""


def extract_text_from_wrapper(response):
    """
    Extracts text from a response wrapped in \\text{...}
    
    Args:
        response (str): The input string containing the \\text wrapper
        
    Returns:
        str: The extracted text, or the original response if no wrapper is found
    """
    # Define the regular expression pattern to match \\text{...}
    pattern = r'\\text\{(.*?)\}'
    
    # Search for the pattern in the response
    match = re.search(pattern, response)
    
    # If a match is found, return the text inside the wrapper
    if match:
        return match.group(1)
    else:
        # If no wrapper is found, return the original response
        return response


def extract_string_list(code_string):
    # Find the positions of the first '[' and last ']'
    start = code_string.find('[')
    end = code_string.rfind(']') + 1  # Include the closing bracket
    
    if start == -1 or end == 0:
        return []  # No list found
    
    # Extract the content between the brackets
    list_content = code_string[start:end]
    
    # Safely evaluate the list using ast.literal_eval
    try:
        string_list = ast.literal_eval(list_content)
        # Verify it's a list of strings
        if isinstance(string_list, list) and all(isinstance(item, str) for item in string_list):
            return string_list
        else:
            return []
    except:
        return []


def contains_chinese(s: str) -> bool:
    """
    Check if a string contains any Chinese characters.
    
    Args:
        s: The string to check
        
    Returns:
        True if Chinese characters are found, False otherwise
    """
    # Unicode ranges for Chinese characters (including extensions)
    chinese_pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002b73f\U0002b740-\U0002b81f\U0002b820-\U0002ceaf]')
    return bool(chinese_pattern.search(s))

        
def extract_json(evaluation_response: str) -> dict:
    """
    Extracts JSON content from a string containing ```json ``` blocks.
    
    Args:
        evaluation_response: String containing the evaluation response with JSON output
        
    Returns:
        Dictionary containing the parsed JSON content
        
    Raises:
        ValueError: If no valid JSON block is found or JSON is malformed
    """
    # Match the first ```json ``` block
    json_match = re.search(r'```json\s*(.*?)\s*```', evaluation_response, re.DOTALL)
    
    if not json_match:
        raise ValueError("No JSON block found in the evaluation response")
    
    json_str = json_match.group(1).strip()
    
    try:
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        return None


# this is only for response diversity
def extract_from_tags(content, tag):
    """
    extract_response
    """
    pattern = rf'<{tag}>(.*?)</{tag}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None  # Return None if no match
    

def extract_completeness_for_sat(text):
    """
    从文本中提取"问题是否完整"和"答案是否完整"的结果
    
    参数:
        text: 包含分析结果的文本字符串
        
    返回:
        一个字典，包含'问题是否完整'和'答案是否完整'的结果
    """
    # 定义正则表达式模式
    question_pattern = r'**问题是否完整**:\s*(是|否)'
    answer_pattern = r'**答案是否完整**:\s*(是|否)'
    
    # 查找匹配
    question_match = re.search(question_pattern, text)
    answer_match = re.search(answer_pattern, text)
    
    # 提取结果
    result = {
        '问题是否完整': question_match.group(1) if question_match else None,
        '答案是否完整': answer_match.group(1) if answer_match else None
    }
    
    return result


def extract_solutions(response_text):
    """ 
    extract_solutions
    """
    
    solutions = []
    tag_matches = list(re.finditer(r'\[Solution(\d+)\]', response_text))
    num_tags = len(tag_matches)
    
    # 验证标签编号是否严格递增
    if num_tags > 0:
        tag_numbers = [int(match.group(1)) for match in tag_matches]
        # 检查是否严格递增（如1,2,3）
        if tag_numbers != sorted(tag_numbers) or len(set(tag_numbers)) != num_tags:
            return []  # 顺序错误或有重复编号，返回空列表
    
    # 提取内容（逻辑保持不变）
    for i, match in enumerate(tag_matches):
        content_start = match.end()
        next_start = tag_matches[i+1].start() if i < num_tags-1 else len(response_text)
        content = response_text[content_start:next_start].strip()
        if content:
            solutions.append(content)
    
    return solutions

    
def process_answer_candidates(data):
    """
    process_answer_candidates
    """

    url = load_balancer.select_api()

    try:            
        k = int(config["generation"]["k"])
        model_name = config["server"]["model_name"]

        sample_info = {
            "k": k,
            "sampled_full_responses": {
                f"{model_name}" : []
            },
            "sampled_boxed_responses": {
                f"{model_name}" : []
            }
        }

        if "meta" not in data:
            data["meta"] = {"zy_answer_check_info" : sample_info}
        else:
            data["meta"]["zy_answer_check_info"] = sample_info
                
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]
        
        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]
        
        prompt = prompt_db.get_answer_generation_prompt(src)
        
        for i in range(k):
            model_completion = send_requests(prompt, url, quick_think=True, model_type="qwen3")
            model_completion = model_completion.strip()
            data["meta"]["zy_answer_check_info"]["sampled_full_responses"][model_name].append(model_completion)
            
            boxed_result = extract_box(model_completion)
            data["meta"]["zy_answer_check_info"]["sampled_boxed_responses"][model_name].append(boxed_result.strip())            
            
        # do the check pass using the full response list or extract boxed version
        # since some questions might not be standard QA question,
        # use full response would be more flexible
        answer_check_type = config["generation"]["answer_check_type"]
        answer_extract_type = config["generation"]["answer_extract_type"]
        
        # assign the corresponding answer extraction type
        input_answer_list = []
        if answer_extract_type == "full":
            input_answer_list = data["meta"]["zy_answer_check_info"]["sampled_full_responses"][model_name]
        elif answer_extract_type == "boxed":
            input_answer_list = data["meta"]["zy_answer_check_info"]["sampled_boxed_responses"][model_name]
        else:
            raise Exception("Invalid answer extraction type")
            
        passed = False

        if answer_check_type == "pass_at_k":
            # get the pass at k version
            pass_at_k_prompt = prompt_db.get_pass_at_k_answer_check_prompt(src, input_answer_list, tgt)
            pass_at_k_result = send_requests(pass_at_k_prompt, url, quick_think=True, model_type="qwen3").strip()

            # print("*" * 50)
            # print(pass_at_k_prompt)
            # print("*" * 50)

            passed = pass_at_k_result == "YES"

        elif answer_check_type == "majority_voting":
            # get the marjority voting version
            marjority_vote_prompt = prompt_db.get_majority_voting_prompt(src, input_answer_list)
            marjority_vote_result = send_requests(marjority_vote_prompt, url, quick_think=True, model_type="qwen3").strip()

            # print("*" * 50)
            # print(marjority_vote_result)
            # print("*" * 50)

            data["meta"]["zy_answer_check_info"]["marjority_vote_full_result"] = marjority_vote_result
            data["meta"]["zy_answer_check_info"]["marjority_vote_result"] = extract_box(marjority_vote_result)

            # check the answer
            answer_check_prompt = prompt_db.get_general_answer_check_prompt(src, marjority_vote_result, tgt)
            answer_check_result = send_requests(answer_check_prompt, url, quick_think=True, model_type="qwen3").strip()

            # print("*" * 50)
            # print(answer_check_result)
            # print("*" * 50)
            
            passed = answer_check_result == "YES"
        else:
            raise Exception("Invalid answer check type")
            
        data["meta"]["zy_answer_check_info"]["passed"] = passed
        data["meta"]["zy_answer_check_info"]["answer_check_type"] = answer_check_type
        data["meta"]["zy_answer_check_info"]["answer_extract_type"] = answer_extract_type
    
    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)
    
    return data


def process_difficulty_quality(data):
    """
    process_difficulty_quality
    """
    url = load_balancer.select_api()
    try:    
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]
        
        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]

        data["zy_meta"] = {}

        # process quality
        quality_prompt = prompt_db.get_quality_check_prompt(src, tgt)
        quality_result = send_requests(quality_prompt, url, quick_think=True, model_type="qwen3").strip()
        quality_result_json = extract_json(quality_result)
        data["zy_meta"]["zy_quality_full"] = quality_result
        data["zy_meta"]["zy_quality_json"] = quality_result_json
        
        # process the math subject
        math_subject_prompt = prompt_db.get_math_subject_prompt(src)
        math_subject_result = send_requests(math_subject_prompt, url, quick_think=True, model_type="qwen3").strip()
        boxed_math_subject_result = extract_box(math_subject_result)
        data["zy_meta"]["zy_math_subject_full"] = math_subject_result
        data["zy_meta"]["zy_math_subject_boxed"] = boxed_math_subject_result

        # process math difficulty
        math_difficulty_prompt = prompt_db.get_math_question_difficulty_prompt(src, tgt)
        math_difficulty_result = send_requests(math_difficulty_prompt, url, quick_think=True, model_type="qwen3").strip()
        boxed_math_difficulty_result = extract_box(math_difficulty_result)
        data["zy_meta"]["zy_math_difficulty_full"] = math_difficulty_result
        data["zy_meta"]["zy_math_difficulty_boxed"] = boxed_math_difficulty_result
    
    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)
    
    return data


def process_response_diversity(data):
    """
    process_response_diversity
    """
    url = load_balancer.select_api()
    try:
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]
        
        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]

        # sample the stuff based on diffculty
        num_solutions = 2
        try:
            if float(data["zy_meta"]["zy_math_difficulty_boxed"]) > 2:
                num_solutions = 3
        except Exception as e:
            pass
                
        rd_prompt = prompt_db.get_response_diversity_prompt(src, tgt, num_solutions)
        rd_result = send_requests(rd_prompt, url, quick_think=True, model_type="qwen3").strip()

        # check if the returned solution is valid
        concrete_result = extract_from_tags(rd_result, "response")
        if concrete_result is None:
            raise ValueError("Error for generating response diversity")

        if "zy_meta" not in data:
            data["zy_meta"] = {}
            
        data["zy_meta"]["zy_response_diverisity"] = {}
        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME] = {}
        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["full_result"] = rd_result
        
        if concrete_result == "accept":
            data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["accept"] = True
        else:
            data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["accept"] = False
            return data
            
        solutions = extract_solutions(rd_result)
        # handle the None elements
        solutions = ["" if s is None else s for s in solutions]

        passed_results = []
        for solution in solutions:
            answer_correctness_prompt = prompt_db.get_answer_correct_prompt(src, solution, tgt)
            passed_result = send_requests(answer_correctness_prompt, url, quick_think=True, model_type="qwen3").strip()
            passed = passed_result == "YES"
            passed_results.append(passed)

        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["sampled_solutions"] = solutions
        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["passed"] = passed_results
        
    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_concept_extraction(data):
    """
    process_concept_extraction
    """
    url = load_balancer.select_api()
    try:
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]
        
        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]

        if data["source"] not in ["olympiads", "olympiads_ref", "amc_aime", "aops_forum"]:
            return None
        
        prompt = prompt_db.get_concept_extraction_prompt(src)
        result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()

        data["concepts_full_results"] = result

    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_single_qa_check(data):
    """
    process_single_qa_check
    """
    url = load_balancer.select_api()
    try:
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]
        
        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]

        
        prompt = prompt_db.get_single_qa_consistency_prompt(src, tgt)
        result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()
        if "zy_meta" not in data:
            data["zy_meta"] = dict()

        data["zy_meta"]["zy_single_qa_check_full"] = result
        data["zy_meta"]["zy_single_qa_check_passed"] = extract_text_from_wrapper(extract_box(result)).strip() == "YES"

    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_answer_correctness(data):
    """
    process_answer_correctness
    """
    url = load_balancer.select_api()
    try:
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]
        
        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]

        
        prompt = prompt_db.get_general_answer_check_prompt(src, data["qwen_final_answer"], tgt)
        result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()

        if "zy_meta" not in data:
            data["zy_meta"] = dict()

        data["zy_meta"]["zy_correctness_check_full"] = result
        data["zy_meta"]["zy_correctness_check_passed"] = result.strip() == "YES"

    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_response_diversity_v2(data):
    """
    process_response_diversity_v2
    """
    url = load_balancer.select_api()
    try:
        # reformat the src and tgt type if necessary
        src = data["src"]
        tgt = data["tgt"]

        if isinstance(src, list):
            src = src[0]
        if isinstance(tgt, list):
            tgt = tgt[0]
        
        num_solutions = 2
        lang = ""
        if contains_chinese(src):
            lang = "zh"
        else:
            lang = "en"

        rd_prompt = prompt_db.get_res_div_v2_prompt(src, lang, num_solutions)
        rd_result = send_requests(rd_prompt, url, quick_think=True, model_type="qwen3").strip()

        # check if the returned solution is valid
        concrete_result = extract_from_tags(rd_result, "response")
        if concrete_result is None:
            raise ValueError("Error for generating response diversity")

        if "zy_meta" not in data:
            data["zy_meta"] = {}
            
        data["zy_meta"]["zy_response_diverisity"] = {}
        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME] = {}
        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["full_result"] = rd_result
        
        if concrete_result == "accept":
            data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["accept"] = True
        else:
            data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["accept"] = False
            return data
            
        solutions = extract_solutions(rd_result)
        # handle the None elements
        solutions = ["" if s is None else s for s in solutions]

        passed_results = []
        for solution in solutions:
            answer_correctness_prompt = prompt_db.get_answer_correct_prompt(src, solution, tgt)
            passed_result = send_requests(answer_correctness_prompt, url, quick_think=True, model_type="qwen3").strip()
            passed = passed_result == "YES"
            passed_results.append(passed)

        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["sampled_solutions"] = solutions
        data["zy_meta"]["zy_response_diverisity"][MODEL_NAME]["passed"] = passed_results

    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_math_subjects(data):
    """
    process_math_subjects
    """
    url = load_balancer.select_api()
    try:
        src = ""
        if "src" in data:
            src = data["src"]
        elif "question" in data:
            src = data["question"]
        elif "problem" in data:
            src = data["problem"]
        
        if src == "":
            raise Exception("Invalid question type")

        prompt = prompt_db.get_math_subject_v2_prompt(src)
        result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()

        data["query_md5"] = hash_text(src)
        data["zy_benchmark_category_full"] = result
        data["zy_benchmark_category_extracted"] = extract_text_from_wrapper(extract_box(result))

    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_low_qual_response(data):
    """
    process_low_qual_response
    """
    retry_threshold = 5
    for i in range(retry_threshold):
        try:
            url = load_balancer.select_api()
            prompt = prompt_db.get_low_qual_prompt_filter(data["src"], data["tgt"])
            result = send_requests(prompt, url, quick_think=False, model_type="gptoss")

            if result == None:
                raise ValueError("Error for requesting gptoss")

            thinking, final_answer = result["thinking"], result["final_answer"]
            if thinking == None or final_answer == None:
                raise ValueError("Error for requesting gptoss")

            cleaned_str = final_answer.strip()
            if cleaned_str.startswith("```json"):
                cleaned_str = cleaned_str[7:] # 移除 "```json" (7个字符)
            if cleaned_str.endswith("```"):
                cleaned_str = cleaned_str[:-3] # 移除 "```" (3个字符)
            
            cleaned_str = cleaned_str.strip()
            parsed_json = json.loads(cleaned_str)
            # print(parsed_json)
            data["is_qualified"] = parsed_json["is_qualified"]
            data["problem_analysis"] = parsed_json["problem_analysis"]
            data["gptoss_thinking"] = thinking
            data["gptoss_final_answer"] = final_answer
            break

        except Exception as e:
            # print(f"An error occurred: {e}")
            # traceback.print_exc()  # Prints the full traceback
            if i >= (retry_threshold-1):
                print(f"An error occurred: {e}")
                print("Failed for all the chances. Give up!!!")
                traceback.print_exc()  # Prints the full traceback
                data["zy_processed_status_failed"] = True
                data["zy_processe_failed_error"] = str(e)
            else:
                print(f"Failed for the {i+1} time. Retrying...")
        finally:
            load_balancer.request_complete(url)

    return data


def process_low_qual_response_v2(data):
    """
    process_low_qual_response_v2
    """
    retry_threshold = 1
    for i in range(retry_threshold):
        try:
            url = load_balancer.select_api()
            prompt = prompt_db.get_low_qual_prompt_filter_v2(data["src"], data["tgt"])
            result = send_requests(prompt, url, quick_think=False, model_type="gptoss")

            if result == None:
                raise ValueError("Error for requesting gptoss")

            thinking, final_answer = result["thinking"], result["final_answer"]
            if thinking == None or final_answer == None:
                raise ValueError("Error for requesting gptoss")

            cleaned_str = final_answer.strip()
            if cleaned_str.startswith("```json"):
                cleaned_str = cleaned_str[7:] # 移除 "```json" (7个字符)
            if cleaned_str.endswith("```"):
                cleaned_str = cleaned_str[:-3] # 移除 "```" (3个字符)
            
            cleaned_str = cleaned_str.strip()
            parsed_json = json.loads(cleaned_str)
            # print(parsed_json)
            data["is_qualified"] = parsed_json["is_qualified"]
            data["problem_analysis"] = parsed_json["problem_analysis"]
            data["failed_category"] = parsed_json["failed_category"]
            data["gptoss_thinking"] = thinking
            data["gptoss_final_answer"] = final_answer
            break

        except Exception as e:
            # print(f"An error occurred: {e}")
            # traceback.print_exc()  # Prints the full traceback
            if i >= (retry_threshold-1):
                print(f"An error occurred: {e}")
                print("Failed for all the chances. Give up!!!")
                traceback.print_exc()  # Prints the full traceback
                data["zy_processed_status_failed"] = True
                data["zy_processe_failed_error"] = str(e)
            else:
                print(f"Failed for the {i+1} time. Retrying...")
        finally:
            load_balancer.request_complete(url)

    return data


def process_low_qual_query(data):
    """
    process_low_qual_query
    """
    retry_threshold = 5
    for i in range(retry_threshold):
        try:
            url = load_balancer.select_api()
            def parse_query_type(prediction):
                pattern = r"\*\*问题是否完整\*\*[:：]['\"“]?(是|否)['\"”]?\s+\*\*问题是否高质\*\*[:：]['\"“]?(是|否)['\"”]?"
                completeness_validation, high_qual_validation = re.search(pattern, prediction, flags=re.S).groups()
                completeness_validation = True if completeness_validation == '是' else False
                high_qual_validation = True if high_qual_validation == '是' else False
                return completeness_validation, high_qual_validation

            base_question = data["src"]
            prompt = prompt_db.get_low_qual_query_filter_prompt_from_lijie(base_question)
            result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()
            data["check_result"] = result

            completeness_validation, high_qual_validation = parse_query_type(result)
            data["passed"] = completeness_validation and high_qual_validation
            break

        except Exception as e:
            # print(f"An error occurred: {e}")
            # traceback.print_exc()  # Prints the full traceback
            if i >= (retry_threshold-1):
                print(f"An error occurred: {e}")
                print("Failed for all the chances. Give up!!!")
                traceback.print_exc()  # Prints the full traceback
                data["zy_processed_status_failed"] = True
                data["zy_processe_failed_error"] = str(e)
            else:
                print(f"Failed for the {i+1} time. Retrying...")
        finally:
            load_balancer.request_complete(url)

    return data


def process_sample_response(data):
    """
    process_sample_response
    """
    url = load_balancer.select_api()
    try:

        prompt = "Please reason step by step and put your final answer in \\boxed tag. Question: " + data["src"]
        result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()
        data["zy_meta"] = {}
        data["zy_meta"]["Qwen3-32B_no_think_response"] = result

    except Exception as e:
        print(f"An error occurred: {e}")
        data["zy_processed_status_failed"] = True
        data["zy_processe_failed_error"] = str(e)
        traceback.print_exc()  # Prints the full traceback
    finally:
        load_balancer.request_complete(url)

    return data


def process_fix_latex(data):
    """
    process_fix_latex
    """

    retry_threshold = 10
    for i in range(retry_threshold):
        try:
            url = load_balancer.select_api()
            if "zy_processed_status_failed" in data:
                raise ValueError("Init data error...")

            prompt = prompt_db.get_fix_latex_prompt(data["src"], data["tgt"])
            result = send_requests(prompt, url, quick_think=True, model_type="qwen3").strip()

            cleaned_str = result.strip()
            if cleaned_str.startswith("```json"):
                cleaned_str = cleaned_str[7:] # 移除 "```json" (7个字符)
            if cleaned_str.endswith("```"):
                cleaned_str = cleaned_str[:-3] # 移除 "```" (3个字符)
            
            cleaned_str = cleaned_str.strip()
            cleaned_str = cleaned_str.replace("\\", "\\\\")
            parsed_json = json.loads(cleaned_str)

            data["zy_fix_latex"] = {}
            data["zy_fix_latex"]["src"] = parsed_json["src"]
            data["zy_fix_latex"]["tgt"] = parsed_json["tgt"]
            data["zy_fix_latex"]["raw"] = result
            data["zy_fix_latex"]["cleaned_json"] = cleaned_str
            break

        except Exception as e:
            # print(f"An error occurred: {e}")
            # traceback.print_exc()  # Prints the full traceback
            if i >= (retry_threshold-1):
                print(f"An error occurred: {e}")
                print("Failed for all the chances. Give up!!!")
                traceback.print_exc()  # Prints the full traceback
                data["zy_processed_status_failed"] = True
                data["zy_processe_failed_error"] = str(e)
            else:
                print(f"Failed for the {i+1} time. Retrying...")
        finally:
            load_balancer.request_complete(url)

    return data


def parse_query_type(prediction):
    query_type = prediction
    valid_types = ["证明(proof)", "选择(choice)", "问答-主观(QA_open)", "问答-客观不唯一(QA_multi)", "多题(multi_question)",
                "判断(yesno)", "问答-客观唯一(QA_unique)"]
    
    if query_type not in valid_types:
        for item in valid_types:
            if item in query_type:
                query_type = item

    if "proof" in query_type.lower():
        query_type = "证明(proof)"
    elif "choice" in query_type.lower():
        query_type = "选择(choice)"
    elif "qa_open" in query_type.lower():
        query_type = "问答-主观(QA_open)"
    elif "qa_multi" in query_type.lower():
        query_type = "问答-客观不唯一(QA_multi)"
    elif "multi_question" in query_type.lower():
        query_type = "多题(multi_question)"
    elif "yesno" in query_type.lower():
        query_type = "判断(yesno)"
    elif "qa_unique" in query_type.lower():
        query_type = "问答-客观唯一(QA_unique)"

    if query_type not in valid_types:
        query_type = None

    return query_type


def parse_query_type_for_qa(prediction):
    pattern = r"\*\*问题是否完整\*\*[:：]['\"“]?(是|否)['\"”]?\s+\*\*答案是否完整\*\*[:：]['\"“]?(是|否)['\"”]?"
    query_validation, answer_validation = re.search(pattern, prediction, flags=re.S).groups()
    query_validation = True if query_validation == '是' else False
    answer_validation = True if answer_validation == '是' else False
    return query_validation, answer_validation


def parse_query_quality_for_query_only(prediction):
    for line in prediction.split("\n"):
        if "output" in line:
            if "高质量" in line:
                return "高质量"
            elif "低质量" in line:
                return "低质量"
            else:
                return None
    return None



def parse_query_reasoning_type(prediction):
    for line in prediction.split("\n"):
        if "output" in line:
            if "重计算型" in line:
                return "重计算型"
            elif "重推理型" in line:
                return "重推理型"
            if "记忆类知识点型" in line:
                return "记忆类知识点型"
            elif "其他" in line:
                return "其他"
            else:
                return None
    return None


def process_low_quality_query_0904(data):
    """
    process_low_quality_query_0904 THIS IS FOR QUERY ONLY
    """
    retry_threshold = 3
    for i in range(retry_threshold):
        try:
            url = load_balancer.select_api()

            if "src" not in data:
                data["src"] = data["Question"]
                
            question = data["src"]
            answer = "缺失"
    
            # get the query quality
            query_quality_prompt = prompt_db.get_low_quality_query_0904_prompt(question)
            quality_result = send_requests(query_quality_prompt, url, quick_think=True, model_type="qwen3").strip()
            quality = parse_query_quality_for_query_only(quality_result)
    
            # get the query question type
            query_question_type_prompt = prompt_db.get_query_type_0904_prompt(question)
            question_type_result = send_requests(query_question_type_prompt, url, quick_think=True, model_type="qwen3").strip()
            query_question_type = parse_query_type(question_type_result)
    
            # get the query reasoning type
            query_reasoning_type_prompt = prompt_db.get_query_reasoning_type_0904_prompt(question, answer)
            query_reasoning_type_result = send_requests(query_reasoning_type_prompt, url, quick_think=True, model_type="qwen3").strip()
            query_reasoning_type = parse_query_reasoning_type(query_reasoning_type_result)
    
            data["zy_meta_info"] = {}
            data["zy_meta_info"]["quality_prediction"] = quality_result
            data["zy_meta_info"]["quality_final_result"] = quality
            data["zy_meta_info"]["question_type_prediction"] = question_type_result
            data["zy_meta_info"]["question_type_final_result"] = query_question_type
            data["zy_meta_info"]["reasoning_type_prediction"] = query_reasoning_type_result
            data["zy_meta_info"]["reasoning_type_final_result"] = query_reasoning_type
            break
    
        except Exception as e:
            # print(f"An error occurred: {e}")
            # traceback.print_exc()  # Prints the full traceback
            if i >= (retry_threshold-1):
                print(f"An error occurred: {e}")
                print("Failed for all the chances. Give up!!!")
                traceback.print_exc()  # Prints the full traceback
                data["zy_processed_status_failed"] = True
                data["zy_processe_failed_error"] = str(e)
            else:
                print(f"Failed for the {i+1} time. Retrying...")
        finally:
            load_balancer.request_complete(url)

    return data


def process_low_quality_qa_0904(data):
    """
    process_low_quality_qa_0904 THIS IS FOR QA QA QA
    """
    retry_threshold = 3
    for i in range(retry_threshold):
        try:
            url = load_balancer.select_api()
            question = data["src"]
            answer = data["original_tgt"]

            # get the query quality
            qa_quality_prompt = prompt_db.get_low_quality_qa_0904_prompt(question, answer)
            quality_result = send_requests(qa_quality_prompt, url, quick_think=True, model_type="qwen3").strip()
            query_validation, answer_validation = parse_query_type_for_qa(quality_result)
            quality = query_validation and answer_validation

            # get the query question type
            query_question_type_prompt = prompt_db.get_query_type_0904_prompt(question)
            question_type_result = send_requests(query_question_type_prompt, url, quick_think=True, model_type="qwen3").strip()
            query_question_type = parse_query_type(question_type_result)

            # get the query reasoning type
            query_reasoning_type_prompt = prompt_db.get_query_reasoning_type_0904_prompt(question, answer)
            query_reasoning_type_result = send_requests(query_reasoning_type_prompt, url, quick_think=True, model_type="qwen3").strip()
            query_reasoning_type = parse_query_reasoning_type(query_reasoning_type_result)

            data["zy_meta_info"] = {}
            data["zy_meta_info"]["quality_prediction"] = quality_result
            data["zy_meta_info"]["quality_final_result"] = quality
            data["zy_meta_info"]["question_type_prediction"] = question_type_result
            data["zy_meta_info"]["question_type_final_result"] = query_question_type
            data["zy_meta_info"]["reasoning_type_prediction"] = query_reasoning_type_result
            data["zy_meta_info"]["reasoning_type_final_result"] = query_reasoning_type
            break

        except Exception as e:
            # print(f"An error occurred: {e}")
            # traceback.print_exc()  # Prints the full traceback
            if i >= (retry_threshold-1):
                print(f"An error occurred: {e}")
                print("Failed for all the chances. Give up!!!")
                traceback.print_exc()  # Prints the full traceback
                data["zy_processed_status_failed"] = True
                data["zy_processe_failed_error"] = str(e)
            else:
                print(f"Failed for the {i+1} time. Retrying...")
        finally:
            load_balancer.request_complete(url)

    return data


def general_sample(data_list, output_file, processing_function):
    """
    general sample
    """
    final_result_list = []
    max_workers = len(API_URL_LIST)

    if config["server"]["forced_num_workers"] > 0:
        max_workers = config["server"]["forced_num_workers"]

    print()
    print("*" * 50)
    print("Started the process!")
    print(f"Number of elements to process: {len(data_list)}")
    print(f"Number of workers: {max_workers}")
    print(f"Number of valid urls: {len(API_URL_LIST)}")
    print("*" * 50)
    print()
    
    # full_data_list_len = len(data_list)
    # expected_process_len = int(len(data_list) * 0.99)
    # current_processed = 0
    
    # print(f"Actual data element length: {full_data_list_len}")
    # print(f"Expected elements to be processed length: {full_data_list_len}")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(processing_function, data) for data in data_list]

        for future in tqdm(as_completed(futures), total=len(data_list)):
            result = future.result()
            if result != None:
                add_jsonl(result, output_file)
                # final_result_list.append(result)

    # write_jsonl(final_result_list, output_file)


def sample(args):
    """ 
    sample
    """

    assert load_balancer is not None

    input_file = args.input_file
    output_file = args.output_file
    data_list = read_jsonl(input_file)

    if len(data_list) == 0:
        print("The length of the input file is zero. Exit.")
        print("Write an empty list directly")
        write_jsonl([], output_file)
        exit(1)

    general_sample(data_list, output_file, process_low_quality_query_0904)


def main():
    """
    main
    """
    args = get_args()
    check_configs()

    # prepare for the api links
    process_api_urls()
    register_workload_balancer()

    # start the real sampling stuff
    sample(args)


if __name__ == '__main__':
    """
    main
    """
    main()






